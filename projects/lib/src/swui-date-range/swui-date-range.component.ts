import { FocusMonitor } from '@angular/cdk/a11y';
import {
  ChangeDetectorRef, Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';
import { MatFormFieldControl } from '@angular/material/form-field';
import { MatMenu, MatMenuTrigger } from '@angular/material/menu';
import { MatTabGroup } from '@angular/material/tabs';
import moment from 'moment';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { distinctUntilChanged, map, takeUntil } from 'rxjs/operators';
import { SwuiDatePickerConfig, SwuiDatePickerConfigModel } from '../swui-date-picker/swui-date-picker-config.model';
import { processInputString } from '../swui-date-picker/swui-date-picker.component';

import { CUSTOM_PERIODS, CustomPeriod, SwuiDateRange, SwuiDateRangeModel } from './swui-date-range.model';
import { MatInput } from '@angular/material/input';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';
import { isValidDateString } from '../common/date-validation.util';


const CONTROL_NAME = 'lib-swui-date-range';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-date-range',
    templateUrl: './swui-date-range.component.html',
    styleUrls: ['./swui-date-range.component.scss'],
    providers: [{ provide: MatFormFieldControl, useExisting: SwuiDateRangeComponent }],
    standalone: false
})
export class SwuiDateRangeComponent extends SwuiMatFormFieldControl<SwuiDateRange> implements OnInit {
  @Input() set customPeriods(customPeriods: CustomPeriod[]) {
    if (customPeriods) {
      this._customPeriods = customPeriods;
    }
  }

  get customPeriods(): CustomPeriod[] {
    return this._customPeriods;
  }

  @Input()
  set value( val: SwuiDateRange ) {
    this.writeValue(val);
  }

  get value(): SwuiDateRange {
    return this._currentControlValue;
  }

  @Input() customClass?: string;

  @Input() minDate = '';
  @Input() maxDate = '';

  @Input()
  set config( val: SwuiDatePickerConfig ) {
    this._config$.next(new SwuiDatePickerConfigModel(val));
  }

  get config(): SwuiDatePickerConfig {
    return this._config$.value;
  }

  @Input() title = '';

  get empty() {
    return !this.valueControl.value;
  }

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;
  readonly controlType = CONTROL_NAME;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return !this.empty;
  }

  @ViewChild('input') input?: MatInput;
  @ViewChild('tabSet', { static: true }) tabsRef: MatTabGroup | undefined;
  @ViewChild('date', { read: MatMenuTrigger }) menuTriggerRef: MatMenuTrigger | undefined;
  @ViewChild('matMenu', { read: MatMenu }) matMenu: MatMenu | undefined;

  readonly valueControl = new UntypedFormControl('');
  readonly form: UntypedFormGroup;
  selectedIndex = 0;
  processedMinDate?: string;
  processedMaxDate?: string;

  private _customPeriods: CustomPeriod[] = CUSTOM_PERIODS;
  private readonly _value$ = new BehaviorSubject<void>(undefined);
  private _currentControlValue = { from: '', to: '' };
  private _sourceValue: SwuiDateRange = new SwuiDateRangeModel();
  private readonly _config$ = new BehaviorSubject<SwuiDatePickerConfig>(new SwuiDatePickerConfigModel(undefined));
  private oldConfig?: SwuiDatePickerConfig;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               private cdr: ChangeDetectorRef,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher,
               fb: UntypedFormBuilder
  ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
    this.form = fb.group({
      from: [],
      to: []
    });
  }

  ngOnInit(): void {
    this.processedMinDate = this.processedMinDate || this.minDate;
    this.processedMaxDate = this.processedMaxDate || this.maxDate;
    this.fromControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.destroyed$)
      )
      .subscribe(from => {
        const fromMoment = from && moment(from);
        const maxPeriod = this.config.chooseStart
          ? this.config.maxPeriod && fromMoment
          && fromMoment.clone()
            .add(1, this.config.maxPeriod).toISOString()
          : this.config.maxPeriod && fromMoment
          && fromMoment.clone()
            .add(1, this.config.maxPeriod).subtract(1, 'seconds').toISOString();
        this.processedMaxDate = this.maxDate
          ? maxPeriod && moment.min(maxPeriod, moment(this.maxDate)) || this.maxDate
          : maxPeriod;
      });
    this.toControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.destroyed$)
      )
      .subscribe(to => {
        const toMoment = to && moment(to);
        let daysOffset = null;

        if (this.config.maxPeriod === 'month' && toMoment) {
          const isLastDay = toMoment.daysInMonth() === toMoment.date();
          let offset = 0;
          const diff = toMoment.date() - toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth();

          if (diff > 0) {
            offset = diff;
          }

          daysOffset = isLastDay
            ? toMoment.clone().daysInMonth()
            : toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth() + offset;
        }

        const minMomentPeriod = this.config.chooseStart
          ? this.config.maxPeriod && toMoment && toMoment.clone()
          : this.config.maxPeriod && toMoment && toMoment.clone().add(1, 'second');
        const minPeriod = daysOffset
          ? this.config.maxPeriod && toMoment && minMomentPeriod?.subtract(daysOffset, 'day').toISOString()
          : this.config.maxPeriod && toMoment && minMomentPeriod?.subtract(1, this.config.maxPeriod).toISOString();

        this.processedMinDate = this.minDate
          ? minPeriod && moment.max(minPeriod, moment(this.minDate)) || this.minDate
          : minPeriod;
      });

    combineLatest([this._value$, this._config$])
      .pipe(
        map(( [, config] ) => {
          let offset = 0;
          const val = this._currentControlValue;

          if (this.oldConfig) {
            if (this.oldConfig !== config && this.oldConfig.timeZone && this.oldConfig.timeZone !== config.timeZone) {
              const oldOffset = Number(moment().tz(this.oldConfig.timeZone).utcOffset());
              const currentOffset = Number(moment().tz(config.timeZone as string).utcOffset());

              offset = oldOffset - currentOffset;
            }
          }

          this.oldConfig = config;

          const from = val && val.from && isValidDateString(val.from) ? val.from : '';
          const fromFormatted = from && moment(from).add(offset, 'minutes').utc().toISOString();
          const to = val && val.to && isValidDateString(val.to) ? val.to : '';
          const toFormatted = to && moment(to).add(offset, 'minutes').milliseconds(0).utc().toISOString();
          const processed = new SwuiDateRangeModel({ from: fromFormatted, to: toFormatted });
          return { processed, config };
        }),
        takeUntil(this.destroyed$)
      )
      .subscribe(( { processed, config } ) => {
        this._sourceValue = processed;
        this._currentControlValue = processed;
        this.form.setValue(processed);

        const { from, to } = processed;
        const formattedFrom = processInputString(from, config);
        const formattedTo = processInputString(to, config);
        const formattedValue = `${formattedFrom}${!!from && !!to ? ' - ' : ''}${formattedTo}`;
        this.valueControl.setValue(formattedValue);
        if (this.config.timeZone && from && to) {
          let fromDate = moment.tz(moment(from), this.config.timeZone);
          let toDate = moment.tz(moment(to), this.config.timeZone);
          const currentDate = moment.tz(moment(), this.config.timeZone);

          fromDate = fromDate.add(fromDate.utcOffset() - currentDate.utcOffset(), 'minutes');
          toDate = toDate.add(toDate.utcOffset() - currentDate.utcOffset(), 'minutes');
          this.onChange({ from: fromDate.toISOString(), to: toDate.toISOString() });
        } else {
          this.onChange(processed);
        }
      });
  }

  onContainerClick( event: Event ): void {
    event.stopPropagation();
    if (this.elRef && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.elRef.nativeElement.focus();
    }
  }

  writeValue( val: SwuiDateRange ): void {
    const { from, to } = val || {};
    let data = val;
    if (this.config.timeZone && from && to) {
      let fromDate = moment.tz(moment(from), this.config.timeZone);
      let toDate = moment.tz(moment(to), this.config.timeZone);
      const currentDate = moment.tz(moment(), this.config.timeZone);

      fromDate = fromDate.add(currentDate.utcOffset() - fromDate.utcOffset(), 'minutes');
      toDate = toDate.add(currentDate.utcOffset() - toDate.utcOffset(), 'minutes');
      data = { from: fromDate.toISOString(), to: toDate.toISOString() };
    }
    this._currentControlValue = new SwuiDateRangeModel(data);
    this._value$.next(undefined);
  }

  get fromControl(): UntypedFormControl {
    return this.form.get('from') as UntypedFormControl;
  }

  get toControl(): UntypedFormControl {
    return this.form.get('to') as UntypedFormControl;
  }

  prevent( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
  }

  onPeriodSelect( period: ( timezone?: string, chooseStart?: boolean ) => SwuiDateRange ) {
    this.form.patchValue(period(this.config?.timeZone, this.config?.chooseStart));
  }

  clear( event: Event ) {
    event.preventDefault();
    this.form.setValue({ from: '', to: '' });
  }

  cancel( event: Event ) {
    event.preventDefault();
    if (this.menuTriggerRef) {
      this.menuTriggerRef.closeMenu();
    }
  }

  apply( event: Event ) {
    event.preventDefault();
    this._currentControlValue = this.form.value;
    this._value$.next(undefined);
    if (this.menuTriggerRef) {
      this.menuTriggerRef.closeMenu();
    }
  }

  onMenuOpen() {
    if (this.tabsRef) {
      this.tabsRef.realignInkBar();
    }
  }

  onMenuClose() {
    this.form.patchValue(this._sourceValue);
  }

  onSelectedIndexChange( tabIndex: number ) {
    this.selectedIndex = tabIndex;
    this.recalculateMenu();
  }

  isSelected( period: CustomPeriod ): boolean {
    return JSON.stringify(period.fn(this.config.timeZone, this.config.chooseStart)) === JSON.stringify(this.form.value);
  }

  protected onDisabledState( disabled: boolean ) {
    if (disabled) {
      this.valueControl.disable();
    } else {
      this.valueControl.enable();
    }
  }

  protected isErrorState(): boolean {
    if (this.input) {
      return this.input.errorState;
    }
    return false;
  }

  private recalculateMenu() {
    window.dispatchEvent(new Event('resize'));
    this.cdr.markForCheck();
  }
}
