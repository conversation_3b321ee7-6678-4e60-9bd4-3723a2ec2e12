import moment from 'moment';

/**
 * Safely validates if a string can be parsed as a valid date by moment.js
 * without triggering deprecation warnings for invalid date strings.
 * 
 * This utility function performs pre-validation checks to avoid creating
 * moment objects for obviously invalid strings, which prevents the
 * deprecation warnings that occur when moment.js falls back to native
 * Date parsing for unrecognized formats.
 * 
 * @param val - The string to validate as a date
 * @returns true if the string can be parsed as a valid date, false otherwise
 */
export function isValidDateString(val: string): boolean {
  if (!val || typeof val !== 'string') {
    return false;
  }
  
  // Basic checks to avoid creating moment objects for obviously invalid strings
  const trimmed = val.trim();
  if (trimmed === '' || trimmed === 'wrong_string' || trimmed === 'invalid') {
    return false;
  }
  
  // Check for basic date patterns to avoid moment deprecation warnings
  // ISO 8601 format: YYYY-MM-DDTHH:mm:ss.sssZ or YYYY-MM-DD
  const isoPattern = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?)?$/;
  // RFC 2822 format patterns
  const rfcPattern = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s+\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4}\s+\d{2}:\d{2}:\d{2}/;
  // Unix timestamp (numbers only)
  const timestampPattern = /^\d+$/;
  
  // If it doesn't match any known valid patterns, return false to avoid warnings
  if (!isoPattern.test(trimmed) && !rfcPattern.test(trimmed) && !timestampPattern.test(trimmed)) {
    return false;
  }
  
  // Only create moment object if it passes basic pattern checks
  try {
    const momentObj = moment.utc(val);
    return momentObj.isValid();
  } catch (error) {
    return false;
  }
}
