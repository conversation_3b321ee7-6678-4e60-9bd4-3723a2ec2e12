#!/bin/bash

# <PERSON>ript to set npm package version based on branch name and existing tags
# This script extracts the minor version from the branch name and finds the latest
# tag for that minor version to use as the base for npm versioning

set -e

# Extract minor version from branch name (e.g., release/0.2.0 -> 0.2)
if [[ "${BRANCH_NAME}" =~ ^release/([0-9]+\.[0-9]+) ]]; then
  MINOR_VERSION="${BASH_REMATCH[1]}"
else
  MINOR_VERSION="0.1"  # Default fallback
fi

echo "Branch: ${BRANCH_NAME}"
echo "Extracted minor version: ${MINOR_VERSION}"

# Get the latest tag for the current minor version
LATEST_TAG=$(git tag --sort=-version:refname | grep "^${MINOR_VERSION}\." | head -n1)

if [ -z "$LATEST_TAG" ]; then
  # If no tags exist for this minor version, start with the minor version + .0
  LATEST_TAG="${MINOR_VERSION}.0"
  echo "No existing tags found for minor version ${MINOR_VERSION}, starting with: ${LATEST_TAG}"
else
  echo "Latest tag for minor version ${MINOR_VERSION}: ${LATEST_TAG}"
fi

# Get current package version
CURRENT_VERSION=$(node -p "require('./package.json').version")
echo "Current package version: ${CURRENT_VERSION}"

# Set the npm package version to the latest tag only if it's different
if [ "$CURRENT_VERSION" != "$LATEST_TAG" ]; then
  echo "Setting npm version to: ${LATEST_TAG}"
  npm version $LATEST_TAG
  echo "Version set successfully"
else
  echo "Version is already set to ${LATEST_TAG}, no change needed"
fi
